<!DOCTYPE html>
<html>

<head>
  <meta http-equiv="content-type" content="text/html;charset=utf-8" />
  <link rel="shortcut icon" href="#" />
  <link rel="stylesheet" type="text/css" href="{{pluResPath}}html/Stat/Stat.css" />
  <link rel="preload" href="{{pluResPath}}script/g2plot.min.js" as="script" />
  <link rel="preload" href="{{pluResPath}}font/tttgbnumber.ttf" as="font">
  <link rel="preload" href="{{pluResPath}}html/DAU/img/bg.webp" as="image">
</head>

<body class="bg">
  <div class="container" id="container">
    <div class="box">
      <div id="chartContainer"></div>
      <ul class="tooltip">
        {{each group val}}
        <li>
          <i style="background: {{val.color}}"></i>
          <span class="action"><em>{{val.name}}</em></span>
          <span class="num"><em>{{val.num}}</em></span>
          <span class="percent"><em>{{val.percent}}</em></span>
        </li>
        {{/each}}
      </ul>
    </div>
  </div>
</body>

<script type="text/javascript" src="{{pluResPath}}script/g2plot.min.js"></script>
<script>
  const { Pie } = G2Plot
  const data = JSON.parse(`{{@ group_by}}`)
  const piePlot = new Pie('chartContainer', {
    renderer: 'svg',
    animation: false,
    data: data,
    appendPadding: 20,
    angleField: 'num',
    colorField: 'name',
    radius: 1,
    innerRadius: 0.7,
    color: JSON.parse(`{{@ color}}`),
    label: {
      type: 'inner',
      offset: '-50%',
      autoRotate: false,
      style: {
        textAlign: 'center',
        fontFamily: 'tttgbnumber',
        'fontSize': 48
      },
      formatter: ({ percent }) => {
        percent = (percent * 100).toFixed(0)
        return `${percent}%`
      }
    },
    statistic: {
      title: {
        offsetY: -18,
        content: '总计',
        style: {
          fontFamily: 'tttgbnumber',
          'fontSize': 64
        }
      },
      content: {
        offsetY: -10,
        style: {
          fontFamily: 'tttgbnumber',
          'fontSize': 96
        }
      }
    },
    legend: false
  })
  piePlot.render()
</script>

</html>