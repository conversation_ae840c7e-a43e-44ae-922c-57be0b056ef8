/**
 * 全局替换md.js - QQ机器人Markdown模板消息转换插件
 */

// 兑换码检测
const REDEEM_CODE_REGEX = /兑换码|礼品码|激活码|cdkey|cd key|redemption code|gift code/i;
const URL_REGEX = /(https?:\/\/[^\s]+)/g;
const KURO_ICU_REGEX = /kuro\.icu/i;

export default class MsgResetMD extends plugin {
  constructor() {
    super({
      name: '全局消息转换MD',
      dsc: 'markdown',
      event: 'message',
      priority: 2000,
    });
  }

  async accept(e) {
    const originalReply = this.e.reply;

    if (['QQBot'].includes(e.adapter_name)) {
      this.e.reply = async function (msgs, quote, data) {
        if (!msgs) return false;
        if (!Array.isArray(msgs)) msgs = [msgs];

        // 检查是否包含特殊消息类型，如果有则直接发送原消息
        const hasSpecialMessage = msgs.some(msg => {
          return msg?.type === 'image' ||
                 msg?.type === 'markdown' ||
                 msg?.type === 'raw' ||
                 msg?.type === 'video' ||
                 msg?.type === 'record' ||
                 msg?.type === 'at';
        });

        if (hasSpecialMessage) {
          return await originalReply.call(this, msgs, quote, data);
        }

        // 处理纯文本消息
        const textMessages = [];
        let hasRedeemCode = false;
        const kuroIcuLinks = [];

        for (const msg of msgs) {
          if (typeof msg === 'string') {
            // 处理字符串类型的消息
            if (REDEEM_CODE_REGEX.test(msg)) {
              hasRedeemCode = true;
              break;
            }

            // 检查kuro.icu链接
            const matches = [...msg.matchAll(URL_REGEX)];
            for (const match of matches) {
              const linkUrl = match[1];
              if (KURO_ICU_REGEX.test(linkUrl)) {
                kuroIcuLinks.push(linkUrl);
              }
            }

            textMessages.push(msg);
          } else if (msg?.type === 'text' && msg?.data?.text) {
            // 处理text类型的消息
            const text = msg.data.text;
            if (REDEEM_CODE_REGEX.test(text)) {
              hasRedeemCode = true;
              break;
            }

            // 检查kuro.icu链接
            const matches = [...text.matchAll(URL_REGEX)];
            for (const match of matches) {
              const linkUrl = match[1];
              if (KURO_ICU_REGEX.test(linkUrl)) {
                kuroIcuLinks.push(linkUrl);
              }
            }

            textMessages.push(text);
          }
        }

        // 如果包含兑换码，直接发送原消息
        if (hasRedeemCode) {
          return await originalReply.call(this, msgs, quote, data);
        }

        // 如果有文本消息，转换为markdown
        if (textMessages.length > 0) {
          const combinedText = textMessages.join('');

          // 检查是否是超能力相关消息
          const isSuperPowerMessage = this.isSuperPowerMessage(combinedText);

          if (isSuperPowerMessage) {
            // 创建超能力markdown消息
            const markdownMessage = this.createSuperPowerMarkdownMessage(combinedText);

            // 创建超能力按钮
            const buttonData = {
              type: 'keyboard',
              id: '102072241_1751292822',
            };

            // 组合markdown和按钮
            const msg = [markdownMessage, segment.raw(buttonData)];

            setTimeout(async () => {
              try {
                await originalReply.call(this, msg, false, { at: false });
              } catch (error) {
                console.error('发送超能力消息出错：', error);
                await originalReply.call(this, [markdownMessage], quote, data);
              }
            }, 1000);

            return;
          }

          const markdownMessage = this.createMarkdownMessage(combinedText);

          // 如果有kuro.icu链接，先发送markdown，再单独发送链接
          if (kuroIcuLinks.length > 0) {
            await originalReply.call(this, [markdownMessage], quote, data);

            // 单独发送kuro.icu链接
            for (const kuroLink of kuroIcuLinks) {
              await originalReply.call(this, [`请复制到浏览器打开:\n\n${kuroLink}`], false, data);
            }
            return;
          } else {
            return await originalReply.call(this, [markdownMessage], quote, data);
          }
        } else {
          return await originalReply.call(this, msgs, quote, data);
        }
      }.bind(this);
    }
  }

  // 检查是否是超能力相关消息
  isSuperPowerMessage(text) {
    const superPowerKeywords = [
      '今日超能力',
      '昨日超能力',
      '明日超能力',
      '超能力指数',
      '按下',
      '不按',
      '评论成功',
      '点赞',
      '点踩'
    ];

    return superPowerKeywords.some(keyword => text.includes(keyword));
  }

  // 创建超能力Markdown消息
  createSuperPowerMarkdownMessage(textMessage) {
    try {
      // 保持原始格式，包括换行和段落
      let formattedText = textMessage.trim();

      // 分行处理，识别不同的内容类型
      const lines = formattedText.split('\n');
      const formattedLines = lines.map(line => {
        const trimmedLine = line.trim();

        if (!trimmedLine) return line; // 保持空行

        // 格式化不同类型的内容
        if (trimmedLine.includes('超能力')) {
          // 超能力标题
          return `#${trimmedLine}`;
        } else if (trimmedLine.includes('你是否会按下这个按钮') || trimmedLine.includes('你会按下这个按钮吗')) {
          // 主要问题 - 移除原有的#号，设置为标题
          const cleanQuestion = trimmedLine.replace(/^#+\s*/, ''); // 移除开头的#号和空格
          return `## ${cleanQuestion}`;
        } else if (trimmedLine.startsWith('#但是:') || trimmedLine.startsWith('但是:')) {
          // "但是"部分标题
          return `### ${trimmedLine}`;
        } else if (trimmedLine.startsWith('>已有') && trimmedLine.includes('选择')) {
          // 统计信息
          return `> ${trimmedLine.substring(1)}`;
        } else if (trimmedLine.includes('你获得了') || trimmedLine.includes('你拥有')) {
          // 能力描述
          return `*${trimmedLine}*`;
        }

        return line; // 其他内容保持原样
      });

      formattedText = formattedLines.join('\n');

      const params = [
        {
          key: 'text',
          values: [formattedText],
        },
      ];

      return segment.markdown({
        custom_template_id: '102072241_1752245006',
        params: params,
      });
    } catch (error) {
      console.error('创建超能力Markdown消息出错：', error);
      return this.createMarkdownMessage(textMessage);
    }
  }

  // 创建普通Markdown消息
  createMarkdownMessage(textMessage) {
    try {
      // 对文本进行转义处理，避免markdown语法字符导致模板参数错误
      let cleanText = textMessage.trim();
      
      // 调试：输出原始文本
      console.log('原始文本:', JSON.stringify(cleanText));
      
      // 移除零宽字符和其他不可见字符
      cleanText = cleanText
        .replace(/\u200B/g, '')    // 零宽空格
        .replace(/\u200C/g, '')    // 零宽非连字符
        .replace(/\u200D/g, '')    // 零宽连字符
        .replace(/\uFEFF/g, '')    // 字节顺序标记
        .replace(/\u00A0/g, ' ')   // 不间断空格转为普通空格
        .replace(/[\u2000-\u200F]/g, ' ')  // 各种空格字符
        .replace(/[\u2028-\u2029]/g, ''); // 行分隔符和段落分隔符直接移除
      
      // 直接移除所有换行符
      cleanText = cleanText
        .replace(/\r\n/g, '')      // 移除Windows换行符
        .replace(/\r/g, '')        // 移除Mac换行符
        .replace(/\n/g, '');       // 移除Unix换行符
      
      // 转义markdown特殊字符
      cleanText = cleanText
        .replace(/\\/g, '\\\\')    // 反斜杠（必须最先处理）
        .replace(/\*/g, '\\*')     // 星号
        .replace(/_/g, '\\_')      // 下划线  
        .replace(/`/g, '\\`')      // 反引号
        .replace(/~/g, '\\~')      // 波浪号
        .replace(/\[/g, '\\[')     // 左方括号
        .replace(/\]/g, '\\]')     // 右方括号
        .replace(/\(/g, '\\(')     // 左圆括号
        .replace(/\)/g, '\\)')     // 右圆括号
        .replace(/#/g, '\\#')      // 井号
        .replace(/>/g, '\\>')      // 大于号
        .replace(/\|/g, '\\|')     // 竖线
        .replace(/\+/g, '\\+')     // 加号
        .replace(/-/g, '\\-')      // 减号
        .replace(/\./g, '\\.')     // 点号
        .replace(/!/g, '\\!')      // 感叹号
        .replace(/\{/g, '\\{')     // 左大括号
        .replace(/\}/g, '\\}')     // 右大括号
        .replace(/=/g, '\\=')      // 等号
        .replace(/\^/g, '\\^')     // 脱字符
        .replace(/&/g, '\\&')      // 与符号
        .replace(/</g, '\\<')      // 小于号
        .replace(/"/g, '\\"')      // 双引号
        .replace(/'/g, "\\'");     // 单引号

      // 调试：输出处理后的文本
      console.log('处理后文本:', JSON.stringify(cleanText));

      const params = [
        {
          key: 'text',
          values: [cleanText],
        },
      ];

      let md = segment.markdown({
        custom_template_id: '102072241_1752245006',
        params: params,
      });

      return md;
    } catch (error) {
      console.error('创建Markdown消息出错：', error);
      return segment.text(textMessage);
    }
  }
}
