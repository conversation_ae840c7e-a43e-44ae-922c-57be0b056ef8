let tuchoice = 5 // 1是原图床获取方法，2是频道图床，3是花瓣图床，4为trss自身图床(开放端口)，文件保存时间前往配置文件bot.yaml自行配置/单位分钟, 5为腾讯图床，需要有野鸡ICQQ在线
let qq = 3764973335 // 替换为实际的QQ号，1是任意野生，2是官鸡，5时填ICQQ的野生
let channelid = 4288864941 // 替换为官鸡的频道ID
let ck = "" // 花瓣网 cookie
let customWords = [
  "签到",
  "芙宁娜面板",
  "米游社全部签到",
  "#扫码登录",
  "哔站签到",
  "哔站功能",
  "哔站登录",
  "菜单",
  "网易功能",
  "#深渊",
  "#练度统计",
  "*练度统计",
  "#角色",
  "*角色",
  "体力",
  "哔站签到记录",
  "我的哔站",
  "#原石预估",
  "芙宁娜伤害",
  "#队伍伤害 角色，角色",
  "#更新面板",
  "*更新面板",
]
import crypto from "crypto"
import imageSize from "image-size"
import { networkInterfaces } from "os"
import fetch from "node-fetch"
let IP

;(async function () {
  try {
    const nets = networkInterfaces()
    const internalIp = Object.values(nets)
      .flatMap(intf => intf)
      .find(net => net.family === "IPv4" && !net.internal)?.address

    if (!internalIp) throw new Error("无法获取内网IP地址")
    const key = internalIp

    let externalIp = await redis.get(key)
    if (!externalIp) {
      const response = await fetch("https://ipinfo.io/json")
      const data = await response.json()
      externalIp = data.ip
      await redis.set(key, externalIp)
    }
    IP = externalIp
  } catch (error) {
    logger.error(error)
  }
})()
export default class MsgReset extends plugin {
  constructor() {
    super({
      name: "全局消息转换",
      dsc: "ark",
      event: "message",
      priority: 2000,
    })
  }

  async accept(e) {
    const originalReply = this.e.reply

    if (["QQBot", "ICQQ"].includes(e.adapter_name)) {
      this.e.reply = async function (msgs, quote, data) {
        if (!msgs) return false
        if (!Array.isArray(msgs)) msgs = [msgs]

        let textMessage = ""
        let imageUrls = []
        let nonTextMessages = []
        let textsummary = []
        const selectedWord = customWords[Math.floor(Math.random() * customWords.length)]
        for (let i = 0; i < msgs.length; i++) {
          let msg = msgs[i]
          if (typeof msg !== "object") {
            msg = {
              type: "text",
              data: {
                text: Bot.String(msg),
              },
            }
          } else {
            msg = {
              ...msg,
            }
          }

          switch (msg.type) {
            case "record":
              return await originalReply(msgs, quote, data)
            case "json":
              return await originalReply(msgs, quote, data)
            case "node":
              return await originalReply(msgs, quote, data)
            case "reply":
              return await originalReply(msgs, quote, data)
            case "video":
              return await originalReply(msgs, quote, data)
            case "markdown":
              return await originalReply(msgs, quote, data)
            case "raw":
              return await originalReply(msgs, quote, data)
            case "ark":
              return await originalReply(msgs, quote, data)
            case "text":
              let modifiedText = msg.data.text
              // if (msg.data.text.includes('扫码')||msg.data.text.includes('登录')||msg.data.text.includes('登陆')) {
              //       textMessage += '请截图二维码前往app扫描\n'
              //  }
              //   if (msg.data.text.includes('http')) {
              const originalText = msg.data.text
              modifiedText = originalText.replace(/\./g, "·")
              //   const wasReplacementMade = originalText !== modifiedText;
              //    if (wasReplacementMade) {
              //       modifiedText += '\n(请将·替换为.后访问链接)';
              // }
              // }

              textMessage += modifiedText
              break
            case "image":
              try {
                let requestBody = msg.file || msg.url
                requestBody = await Bot.Buffer(requestBody)
                const base64String = requestBody.toString("base64")

                let { width, height } = await this.getImageSize(requestBody)
                logger.mark(`图片高度为${width} X ${height}`)
                if (height > 3950) {
                  logger.warn(`图片高度${height}超过3950像素，跳过处理`)
                  await originalReply(msgs, quote, data)
                  return false
                }
                let summary = (msg.summary || "").trim()
                if (summary) {
                  textsummary.push(summary)
                }
                logger.mark(summary.length)
                if (
                  summary &&
                  !["图片", "动画表情"].includes(summary) &&
                  summary.length < 25 &&
                  !textMessage.includes(summary)
                ) {
                  textMessage += summary + "\n"
                }
                let imageUrl
                if (tuchoice === 1) {
                  imageUrl = await this.getTencentImageUrl(base64String)
                } else if (tuchoice === 2) {
                  imageUrl = await this.img_cn(requestBody)
                } else if (tuchoice === 3) {
                  imageUrl = await this.img_hb(requestBody)
                } else if (tuchoice === 4) {
                  imageUrl = await Bot.fileToUrl(requestBody)
                  imageUrl = imageUrl.replace("localhost", IP)
                  logger.mark(`本机TRSS图片URL： ${imageUrl}`)
                } else if (tuchoice === 5) {
                  const image = await Bot[qq].uploadImage(await Bot.fileToUrl(requestBody))
                  imageUrl = image.url.replace("https", "http")
                  logger.mark(`腾讯图床URL： ${imageUrl}`)
                }

                if (imageUrl) {
                  imageUrls.push(imageUrl)
                }
              } catch (error) {
                logger.error("图片转换失败:", error)
              }
              break
            case "button":
              break
            case "at":
              break
            default:
              nonTextMessages.push(msg)
              break
          }
        }

        let msgop = e.msg
        let msgops = msgop
        if (msgop.includes("http")) {
          let replacedMsg = msgop.replace(/\./g, "·")
          if (replacedMsg !== msgop) {
            msgops = replacedMsg + "\n(请将·替换为.后访问链接)"
          }
        }

        if (nonTextMessages.length > 0) {
          logger.mark(`wen：${nonTextMessages.length}`)
          if (imageUrls.length > 0 && textMessage.trim().length > 40) {
            const textArkMessage = segment.raw({
              type: "ark",
              template_id: 23,
              kv: [
                {
                  key: "#PROMPT#",
                  value: "官方群聊218277938",
                },
                {
                  key: "#LIST#",
                  obj: [
                    {
                      obj_kv: [
                        {
                          key: "desc",
                          value: textMessage.trim() ? textMessage.trim() : `宝宝好厉害啊~`,
                        },
                      ],
                    },
                  ],
                },
              ],
            })
            await originalReply([textArkMessage, ...nonTextMessages], quote, data)

            for (let i = 0; i < imageUrls.length; i++) {
              const imageUrl = imageUrls[i]
              const arkMessage = segment.raw({
                type: "ark",
                template_id: 37,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: textsummary[0] || "ZY - YYDS",
                  },
                  {
                    key: "#METATITLE#",
                    value: `${msgops}(宝宝还能艾特我发送『${selectedWord} 』哦~)`,
                  },
                  {
                    key: "#METASUBTITLE#",
                    value: `宝宝好厉害啊~`,
                  },
                  {
                    key: "#METACOVER#",
                    value: imageUrl,
                  },
                ],
              })

              await originalReply([arkMessage], quote, data)
            }
          } else if (imageUrls.length > 0) {
            for (let i = 0; i < imageUrls.length; i++) {
              const imageUrl = imageUrls[i]
              const arkMessage = segment.raw({
                type: "ark",
                template_id: 37,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: textsummary[0] || "ZY - YYDS",
                  },
                  {
                    key: "#METATITLE#",
                    value: `${msgops}(宝宝还能艾特我发送『${selectedWord} 』哦~)`,
                  },
                  {
                    key: "#METASUBTITLE#",
                    value: textMessage.trim() ? textMessage.trim() : `宝宝好厉害啊~`,
                  },
                  {
                    key: "#METACOVER#",
                    value: imageUrl,
                  },
                ],
              })

              await originalReply([arkMessage], quote, data)
            }
            await originalReply([nonTextMessages], quote, data)
          } else {
            if (textMessage.trim()) {
              const textArkMessage = segment.raw({
                type: "ark",
                template_id: 23,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: "官方群聊218277938",
                  },
                  {
                    key: "#LIST#",
                    obj: [
                      {
                        obj_kv: [
                          {
                            key: "desc",
                            value: textMessage.trim() ? textMessage.trim() : `宝宝好厉害啊~`,
                          },
                        ],
                      },
                    ],
                  },
                ],
              })
              await originalReply([...nonTextMessages, textArkMessage], quote, data)
            } else {
              await originalReply(msgs, quote, data)
            }
          }
        } else if (nonTextMessages.length === 0) {
          if (imageUrls.length > 0 && textMessage.trim().length > 25) {
            const textArkMessage = segment.raw({
              type: "ark",
              template_id: 23,
              kv: [
                {
                  key: "#PROMPT#",
                  value: "官方群聊218277938",
                },
                {
                  key: "#LIST#",
                  obj: [
                    {
                      obj_kv: [
                        {
                          key: "desc",
                          value: textMessage.trim() ? textMessage.trim() : `宝宝好厉害啊~`,
                        },
                      ],
                    },
                  ],
                },
              ],
            })
            await originalReply([textArkMessage], quote, data)

            for (let i = 0; i < imageUrls.length; i++) {
              const imageUrl = imageUrls[i]
              const arkMessage = segment.raw({
                type: "ark",
                template_id: 37,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: textsummary[0] || "官方群聊218277938",
                  },
                  {
                    key: "#METATITLE#",
                    value: `${msgops}(宝宝还能艾特我发送『${selectedWord} 』哦~)`,
                  },
                  {
                    key: "#METASUBTITLE#",
                    value: `官方群聊218277938`,
                  },
                  {
                    key: "#METACOVER#",
                    value: imageUrl,
                  },
                ],
              })

              await originalReply([arkMessage], quote, data)
            }
          } else if (imageUrls.length > 0) {
            for (let i = 0; i < imageUrls.length; i++) {
              const imageUrl = imageUrls[i]
              const arkMessage = segment.raw({
                type: "ark",
                template_id: 37,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: textsummary[0] || "ZY - YYDS",
                  },
                  {
                    key: "#METATITLE#",
                    value: `${msgops}(宝宝还能艾特我发送『${selectedWord} 』哦~)`,
                  },
                  {
                    key: "#METASUBTITLE#",
                    value: textMessage.trim() ? textMessage.trim() : "官方群聊218277938",
                  },
                  {
                    key: "#METACOVER#",
                    value: imageUrl,
                  },
                ],
              })

              await originalReply([arkMessage], quote, data)
            }
          } else {
            if (textMessage.trim()) {
              const textArkMessage = segment.raw({
                type: "ark",
                template_id: 23,
                kv: [
                  {
                    key: "#PROMPT#",
                    value: "官方群聊218277938",
                  },
                  {
                    key: "#LIST#",
                    obj: [
                      {
                        obj_kv: [
                          {
                            key: "desc",
                            value: textMessage.trim() ? textMessage.trim() : `宝宝好厉害啊~`,
                          },
                        ],
                      },
                    ],
                  },
                ],
              })
              await originalReply([textArkMessage], quote, data)
            } else {
              await originalReply(msgs, quote, data)
            }
          }
        }
      }.bind(this)
    } else {
      return false
    }
  }

  async getTencentImageUrl(base64Image) {
    try {
      const cookiesAndBkn = await this.getOrExtractCookies(qq)
      const parsedCookies = this.extractCookies(cookiesAndBkn.cookies)
      if (!parsedCookies) {
        throw new Error("缺少必要的Cookie信息")
      }

      const result = await this.uploadImageToTencent(base64Image, parsedCookies)
      if (result.code === 200) {
        return result.url
      } else {
        throw new Error(`上传失败：${result.msg}`)
      }
    } catch (error) {
      logger.error(error)
      return null
    }
  }

  extractCookies(cookiesStr) {
    const cookies = {}
    cookiesStr.split("; ").forEach(cookie => {
      const [key, value] = cookie.split("=")
      cookies[key] = value
    })

    if (!cookies.uin || !cookies.skey || !cookies.p_skey) {
      return null
    }
    return {
      uin: cookies.uin,
      skey: cookies.skey,
      p_skey: cookies.p_skey,
    }
  }

  async getOrExtractCookies(uin) {
    let cookies
    if (Bot[uin].cookies && Bot[uin].cookies["qun.qq.com"]) {
      cookies = Bot[uin].cookies["qun.qq.com"]
      return {
        cookies,
      }
    }
    const apiResult = await Bot[uin].sendApi("get_cookies", {
      domain: "qun.qq.com",
    })
    if (apiResult.retcode === 0) {
      cookies = this.extractCookie(apiResult.data.cookies)
      return {
        cookies,
      }
    }

    return null
  }

  extractCookie(cookiesStr) {
    const cookies = {}
    cookiesStr.split("; ").forEach(cookie => {
      const [key, value] = cookie.split("=")
      cookies[key] = value
    })

    if (!cookies.uin || !cookies.skey || !cookies.p_skey) {
      return null
    }

    return `uin=${cookies.uin}; skey=${cookies.skey}; p_skey=${cookies.p_skey}; p_uin=${cookies.uin}`
  }

  GTK(skey) {
    let hash = 5381
    for (let i = 0; i < skey.length; i++) {
      hash += (((hash << 5) & 2147483647) + skey.charCodeAt(i)) & 2147483647
      hash &= 2147483647
    }
    return hash
  }

  async uploadImageToTencent(base64Image, parsedCookies) {
    if (!parsedCookies.uin || !parsedCookies.skey || !parsedCookies.p_skey) {
      return {
        code: -1,
        msg: "UIN, SKEY and P_SKEY are required.",
      }
    }

    const sbkn = this.GTK(parsedCookies.skey)
    const qun_c = `uin=${parsedCookies.uin}; skey=${parsedCookies.skey}; p_skey=${parsedCookies.p_skey}; p_uin=${parsedCookies.uin}`

    try {
      const formData = new URLSearchParams()
      formData.append("pic", base64Image)
      formData.append("client_type", "1")
      formData.append("bkn", sbkn)

      const response = await fetch("https://qun.qq.com/cgi-bin/hw/util/image", {
        method: "POST",
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) QQ/9.7.1.28934 Chrome/43.0.2357.134 Safari/537.36 QBCore/3.43.1298.400 QQBrowser/9.0.2524.400",
          Origin: "https://qun.qq.com",
          Referer: "https://qun.qq.com/homework/p/features/index.html",
          Cookie: qun_c,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData.toString(),
        redirect: "follow",
        timeout: 120000,
      })

      const data = await response.json()

      if (data.retcode !== 0) {
        return {
          code: data.retcode,
          msg: data.msg,
        }
      }

      const purl = data.data.url.origin
      const finalUrl = purl.replace("p.qpic.cn", "p.qlogo.cn")

      return {
        code: 200,
        url: finalUrl,
      }
    } catch (error) {
      console.error(error)
      return {
        code: -1,
        msg: error.message,
      }
    }
  }

  async img_cn(data) {
    const channelId = channelid

    const blob = new Blob([data], {
      type: "image/png",
    })
    const formData = new FormData()
    formData.append("msg_id", "0")
    formData.append("file_image", blob, "image.jpg")

    try {
      const res = await fetch(`https://api.sgroup.qq.com/channels/${channelId}/messages`, {
        method: "POST",
        headers: {
          Authorization: "QQBot " + Bot[qq].sdk.sessionManager.access_token,
          "X-Union-Appid": Bot[qq].info.appid,
        },
        body: formData,
      })
      const respond = await res.json()
      const md5 = crypto.createHash("md5").update(data).digest("hex").toUpperCase()
      const url = `https://gchat.qpic.cn/qmeetpic/0/0-0-${md5}/0`
      logger.mark(`频道图床URL： ${url}`)
      logger.mark(respond)
      return url
    } catch (error) {
      console.error(error)
      return null
    }
  }

  async img_hb(data) {
    const formdata = new FormData()
    formdata.append(
      "file",
      new Blob([data], {
        type: "image/jpeg",
      }),
      {
        filename: Date.now(), // 上传的文件名
        contentType: "image/jpeg", // 文件类型标识
      },
    )

    try {
      const res = await fetch("https://api.huaban.com/upload", {
        method: "POST",
        body: formdata,
        headers: {
          Cookie: ck,
        },
      })
      const respond = await res.json()
      //  logger.mark(respond)
      const url = `https://gd-hbimg.huaban.com/${respond.key}_fw1200`
      logger.mark(`花瓣图床URL： ${url}`)
      return url
    } catch (error) {
      console.error(error)
      return null
    }
  }

  async getImageSize(buffer) {
    try {
      return imageSize(new Uint8Array(buffer))
    } catch (error) {
      logger.error("获取图片尺寸失败:", error)
      return {
        width: 0,
        height: 0,
      }
    }
  }
}
