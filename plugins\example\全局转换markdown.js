// ==================== 配置区域 ====================
const config = {
  // 基础设置
  enable: true, // 是否启用Markdown转换
  qq: 1325141435, // 用于腾讯图床的QQ号
  enableTencentImageBed: true, // 是否启用腾讯图床

  // 转换开关
  conversion: {
    textToMd: true, // 是否启用纯文本转Markdown
    imageToMd: true, // 是否启用纯图片转Markdown（会添加默认文字转为图文模式）
    mixToMd: true, // 是否启用图文混排转Markdown
  },

  // 单图片默认文字设置
  imageDefaultText: {
    textStart: " ", // 图片前的默认文字（不能为空，至少一个空格）
    textEnd: " ", // 图片后的默认文字（不能为空，至少一个空格）
  },

  // 图片缩放设置（参考QQBot插件）
  markdownImgScale: 1.0, // 图片缩放比例，0.5为缩小50%，1.0为原图，1.5为放大50%

  // 自定义图片尺寸设置
  customImageSize: {
    enabled: true, // 是否启用自定义尺寸
    maxHeight: 1500, // 最大高度限制，超出此高度时显示一半
    halfHeight: 700, // 超出最大高度时显示的高度
  },

  // Markdown模板配置
  templates: {
    text: {
      id: "102072241_1752245006", // 文本模板ID
      textKey: "text", // 文本内容的参数key，对应模板中的 {{.text}}
    },
    mix: {
      id: "102072241_1752245690", // 图文模板ID（纯图片也使用此模板）
      textStartKey: "text_start", // 开头文字的参数key
      textEndKey: "text_end", // 结束文字的参数key
      imageUrlKey: "img_url", // 图片URL的参数key
      imageDescKey: "img_dec", // 图片描述的参数key
    },
    superPower: {
      id: "102072241_1752245006", // 超能力使用文本模板
      textKey: "text", // 文本内容的参数key
      buttonId: "102072241_1751292822", // 超能力按钮ID
    },
  },

  // 白名单设置
  whitelist: {
    enabled: false, // 是否启用白名单
    bots: [], // 机器人QQ号列表
  },

  // 黑名单设置
  blacklist: {
    enabled: false, // 是否启用黑名单
    bots: [], // 机器人QQ号列表
  },

  // 高级设置
  advanced: {
    image: {
      maxHeight: 10000, // 图片最大高度限制
      timeout: 120000, // 图片上传超时时间
    },
    text: {
      replaceNewline: true, // 是否将\n替换为\r
      maxLength: 4000, // 文本最大长度限制
    },
    error: {
      fallbackToOriginal: true, // 转换失败时回退到原始消息
      logErrors: true, // 是否记录错误日志
    },
  },
}

import imageSize from "image-size"
import fetch from "node-fetch"

export default class MarkdownConverter extends plugin {
  constructor() {
    super({
      name: "全局Markdown转换",
      dsc: "将云崽发送的消息转换为特定模板md再发送",
      event: "message",
      priority: 2000,
    })
  }

  async accept(e) {
    // 检查是否启用
    if (!config.enable) {
      return false
    }

    // 只处理QQBot适配器
    if (!["QQBot"].includes(e.adapter_name)) {
      return false
    }

    // 检查白名单
    if (config.whitelist?.enabled && !config.whitelist.bots.includes(String(e.self_id))) {
      return false
    }

    // 检查黑名单
    if (config.blacklist?.enabled && config.blacklist.bots.includes(String(e.self_id))) {
      return false
    }

    const originalReply = this.e.reply

    this.e.reply = async function (msgs, quote, data) {
      if (!msgs) return false
      if (!Array.isArray(msgs)) msgs = [msgs]

      try {
        // logger.mark(`[Markdown转换] 开始处理消息，消息数量: ${msgs.length}`)
        const convertedMsgs = await this.convertToMarkdown(msgs)

        if (convertedMsgs && convertedMsgs.length > 0 && convertedMsgs !== msgs) {
          // logger.mark(`[Markdown转换] 转换成功，生成 ${convertedMsgs.length} 条Markdown消息`)
          return await originalReply(convertedMsgs, quote, data)
        } else {
          // logger.warn(`[Markdown转换] 转换失败或无需转换，使用原始消息`)
          return await originalReply(msgs, quote, data)
        }
      } catch (error) {
        if (config.advanced?.error?.logErrors) {
          // logger.error(`[Markdown转换] 转换过程发生错误:`, error)
          // logger.error(`[Markdown转换] 错误详情: ${error.message}`)
          // logger.error(`[Markdown转换] 错误堆栈: ${error.stack}`)
        }
        if (config.advanced?.error?.fallbackToOriginal) {
          // logger.warn(`[Markdown转换] 回退到原始消息发送`)
          return await originalReply(msgs, quote, data)
        }
        // logger.error(`[Markdown转换] 转换失败且未启用回退，消息发送失败`)
        return false
      }
    }.bind(this)

    return true
  }

  async convertToMarkdown(msgs) {
    // logger.mark(`[Markdown转换] 开始分析消息内容，消息数量: ${msgs.length}`)
    let textContent = ""
    let hasOtherTypes = false

    // 预处理消息，分离图片和其他类型
    const imageMessages = []
    const otherMessages = []

    for (let msg of msgs) {
      if (typeof msg !== "object") {
        msg = { type: "text", data: { text: Bot.String(msg) } }
        // logger.mark(`[Markdown转换] 转换非对象消息为文本类型`)
      } else {
        msg = { ...msg }
      }

      if (msg.type === "image") {
        imageMessages.push(msg)
      } else {
        otherMessages.push(msg)
      }
    }

    // 快速处理非图片消息
    for (const msg of otherMessages) {
      switch (msg.type) {
        case "text":
          const text = msg.data?.text || msg.text || ""
          textContent += text
          // logger.mark(
          //   `[Markdown转换] 文本内容: ${text.substring(0, 50)}${text.length > 50 ? "..." : ""}`,
          // )
          break
        case "at":
          // 忽略@消息，不进行处理
          // logger.mark(`[Markdown转换] 忽略@消息`)
          break
        case "reply":
        case "node":
        case "json":
        case "record":
        case "video":
        case "markdown":
        case "raw":
        case "ark":
          // logger.warn(`[Markdown转换] 检测到不支持的消息类型: ${msg.type}`)
          hasOtherTypes = true
          break
      }
    }

    // 并行处理所有图片消息
    let imageUrls = []
    if (imageMessages.length > 0) {
      try {
        imageUrls = await this.processImagesParallel(imageMessages)
      } catch (error) {
        // logger.error(`[Markdown转换] 图片并行处理失败:`, error)
        hasOtherTypes = true
      }
    }
    // logger.mark(
    //   `[Markdown转换] 消息分析完成 - 文本长度: ${textContent.length}, 图片数量: ${imageUrls.length}, 有其他类型: ${hasOtherTypes}`,
    // )
    // 根据内容类型生成对应的Markdown消息
    return this.generateMarkdownMessage(textContent, imageUrls, hasOtherTypes, msgs)
  }

  generateMarkdownMessage(textContent, imageUrls, hasOtherTypes, originalMsgs) {
    // logger.mark(`[Markdown转换] 开始生成Markdown消息`)
    const hasText = textContent.trim().length > 0
    const hasImages = imageUrls.length > 0

    // logger.mark(
    //   `[Markdown转换] 内容分析 - 有文本: ${hasText}, 有图片: ${hasImages}, 有其他类型: ${hasOtherTypes}`,
    // )

    // 检查文本中是否包含链接
    if (hasText && this.hasLinks(textContent)) {
      // logger.warn(`[Markdown转换] 检测到文本包含链接，回退到原始消息`)
      return originalMsgs
    }

    // 如果有其他类型的消息，回退到原始消息
    if (hasOtherTypes) {
      // logger.warn(`[Markdown转换] 检测到不支持的消息类型，回退到原始消息`)
      return originalMsgs
    }

    let markdownMsgs = []

    // 检查是否是超能力相关消息
    if (hasText && this.isSuperPowerMessage(textContent)) {
      // 超能力模式
      const markdownMessage = this.createSuperPowerMarkdownMessage(textContent)

      // 创建超能力按钮
      const buttonData = {
        type: "keyboard",
        id: config.templates.superPower.buttonId,
      }

      // 组合markdown和按钮
      markdownMsgs.push(markdownMessage)
      markdownMsgs.push(segment.raw(buttonData))
    } else if (hasText && hasImages && config.conversion.mixToMd) {
      // 图文混排模式
      // logger.mark(`[Markdown转换] 使用图文混排模式，模板ID: ${config.templates.mix.id}`)
      // 处理文本内容
      let processedText = textContent
      if (config.advanced?.text?.maxLength && textContent.length > config.advanced.text.maxLength) {
        // logger.warn(
        //   `[Markdown转换] 文本长度${textContent.length}超过限制${config.advanced.text.maxLength}，进行截断`,
        // )
        processedText = textContent.substring(0, config.advanced.text.maxLength) + "..."
      }

      // logger.mark(`[Markdown转换] 开始生成${imageUrls.length}条图文混排消息`)
      for (let imageInfo of imageUrls) {
        // 简单分割：第一行作为开头，其余作为结尾
        const lines = processedText.split("\n")
        const textStart = lines[0] || ""
        const textEnd = lines.length > 1 ? lines.slice(1).join("\n") : ""

        const params = [
          {
            key: config.templates.mix.textStartKey,
            values: [
              config.advanced?.text?.replaceNewline ? textStart.replace(/\n/g, "\r") : textStart,
            ],
          },
          {
            key: config.templates.mix.textEndKey,
            values: [
              config.advanced?.text?.replaceNewline ? textEnd.replace(/\n/g, "\r") : textEnd,
            ],
          },
          {
            key: config.templates.mix.imageUrlKey,
            values: [imageInfo.url],
          },
          {
            key: config.templates.mix.imageDescKey,
            values: [`图片 #${imageInfo.width}px #${imageInfo.height}px`],
          },
        ]

        // logger.mark(
        //   `[调试] 图文混排参数: img_dec = "图片 #${imageInfo.width}px #${imageInfo.height}px"`,
        // )

        markdownMsgs.push(
          segment.markdown({
            custom_template_id: config.templates.mix.id,
            params,
          }),
        )
      }
    } else if (hasImages && config.conversion.imageToMd) {
      // 纯图片模式 - 使用图文混排模板，添加默认文字
      // logger.mark(
      //   `[Markdown转换] 使用纯图片模式（转为图文混排），模板ID: ${config.templates.mix.id}`,
      // )
      // logger.mark(`[Markdown转换] 开始生成${imageUrls.length}条图片消息，添加默认文字`)
      for (let imageInfo of imageUrls) {
        const params = [
          {
            key: config.templates.mix.textStartKey,
            values: [config.imageDefaultText.textStart],
          },
          {
            key: config.templates.mix.textEndKey,
            values: [config.imageDefaultText.textEnd],
          },
          {
            key: config.templates.mix.imageUrlKey,
            values: [imageInfo.url],
          },
          {
            key: config.templates.mix.imageDescKey,
            values: [`图片 #${imageInfo.width}px #${imageInfo.height}px`],
          },
        ]

        // logger.mark(
        //   `[调试] 纯图片参数: img_dec = "图片 #${imageInfo.width}px #${imageInfo.height}px"`,
        // )

        markdownMsgs.push(
          segment.markdown({
            custom_template_id: config.templates.mix.id,
            params,
          }),
        )
      }
    } else if (hasText && config.conversion.textToMd) {
      // 纯文本模式
      // logger.mark(`[Markdown转换] 使用纯文本模式，模板ID: ${config.templates.text.id}`)
      // 检查文本长度限制
      let processedText = textContent
      if (config.advanced?.text?.maxLength && textContent.length > config.advanced.text.maxLength) {
        // logger.warn(
        //   `[Markdown转换] 文本长度${textContent.length}超过限制${config.advanced.text.maxLength}，进行截断`,
        // )
        processedText = textContent.substring(0, config.advanced.text.maxLength) + "..."
      }

      const params = [
        {
          key: config.templates.text.textKey,
          values: [
            config.advanced?.text?.replaceNewline
              ? processedText.replace(/\n/g, "\r")
              : processedText,
          ],
        },
      ]

      markdownMsgs.push(
        segment.markdown({
          custom_template_id: config.templates.text.id,
          params,
        }),
      )
    } else {
      // logger.warn(`[Markdown转换] 无匹配的转换模式或转换开关已关闭`)
      // logger.warn(
      //   `[Markdown转换] 转换开关状态 - 文本: ${config.conversion.textToMd}, 图片: ${config.conversion.imageToMd}, 图文: ${config.conversion.mixToMd}`,
      // )
    }

    if (markdownMsgs.length > 0) {
      // logger.mark(`[Markdown转换] 成功生成${markdownMsgs.length}条Markdown消息`)
      return markdownMsgs
    } else {
      // logger.warn(`[Markdown转换] 未生成任何Markdown消息，返回原始消息`)
      return originalMsgs
    }
  }

  // 腾讯图床相关方法 - 优化版本，支持重试
  async getTencentImageUrl(base64Image, retries = 2) {
    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const cookiesAndBkn = await this.getOrExtractCookies(config.qq)
        const parsedCookies = this.extractCookies(cookiesAndBkn.cookies)
        if (!parsedCookies) {
          throw new Error("缺少必要的Cookie信息")
        }

        const result = await this.uploadImageToTencent(base64Image, parsedCookies)
        if (result.code === 200) {
          // logger.mark(`腾讯图床URL: ${result.url}`)
          return result.url
        } else {
          throw new Error(`上传失败：${result.msg}`)
        }
      } catch (error) {
        if (attempt === retries) {
          // logger.error(`腾讯图床上传失败，已重试${retries}次:`, error.message)
          return null
        }
        // logger.warn(`腾讯图床第${attempt + 1}次尝试失败，准备重试:`, error.message)
        // 等待一段时间后重试，避免频繁请求
        await new Promise(resolve => setTimeout(resolve, 500 * (attempt + 1)))
      }
    }
    return null
  }

  extractCookies(cookiesStr) {
    const cookies = {}
    cookiesStr.split("; ").forEach(cookie => {
      const [key, value] = cookie.split("=")
      cookies[key] = value
    })

    if (!cookies.uin || !cookies.skey || !cookies.p_skey) {
      return null
    }
    return {
      uin: cookies.uin,
      skey: cookies.skey,
      p_skey: cookies.p_skey,
    }
  }

  async getOrExtractCookies(uin) {
    let cookies
    if (Bot[uin].cookies && Bot[uin].cookies["qun.qq.com"]) {
      cookies = Bot[uin].cookies["qun.qq.com"]
      return { cookies }
    }
    const apiResult = await Bot[uin].sendApi("get_cookies", {
      domain: "qun.qq.com",
    })
    if (apiResult.retcode === 0) {
      cookies = this.extractCookie(apiResult.data.cookies)
      return { cookies }
    }
    return null
  }

  extractCookie(cookiesStr) {
    const cookies = {}
    cookiesStr.split("; ").forEach(cookie => {
      const [key, value] = cookie.split("=")
      cookies[key] = value
    })

    if (!cookies.uin || !cookies.skey || !cookies.p_skey) {
      return null
    }

    return `uin=${cookies.uin}; skey=${cookies.skey}; p_skey=${cookies.p_skey}; p_uin=${cookies.uin}`
  }

  GTK(skey) {
    let hash = 5381
    for (let i = 0; i < skey.length; i++) {
      hash += (((hash << 5) & 2147483647) + skey.charCodeAt(i)) & 2147483647
      hash &= 2147483647
    }
    return hash
  }

  async uploadImageToTencent(base64Image, parsedCookies) {
    if (!parsedCookies.uin || !parsedCookies.skey || !parsedCookies.p_skey) {
      return {
        code: -1,
        msg: "UIN, SKEY and P_SKEY are required.",
      }
    }

    const sbkn = this.GTK(parsedCookies.skey)
    const qun_c = `uin=${parsedCookies.uin}; skey=${parsedCookies.skey}; p_skey=${parsedCookies.p_skey}; p_uin=${parsedCookies.uin}`

    try {
      const formData = new URLSearchParams()
      formData.append("pic", base64Image)
      formData.append("client_type", "1")
      formData.append("bkn", sbkn)

      const response = await fetch("https://qun.qq.com/cgi-bin/hw/util/image", {
        method: "POST",
        headers: {
          "User-Agent":
            "Mozilla/5.0 (Windows NT 6.2; WOW64) AppleWebKit/537.36 (KHTML, like Gecko) QQ/9.7.1.28934 Chrome/43.0.2357.134 Safari/537.36 QBCore/3.43.1298.400 QQBrowser/9.0.2524.400",
          Origin: "https://qun.qq.com",
          Referer: "https://qun.qq.com/homework/p/features/index.html",
          Cookie: qun_c,
          "Content-Type": "application/x-www-form-urlencoded",
        },
        body: formData.toString(),
        redirect: "follow",
        timeout: 120000,
      })

      const data = await response.json()

      if (data.retcode !== 0) {
        return {
          code: data.retcode,
          msg: data.msg,
        }
      }

      const purl = data.data.url.origin
      const finalUrl = purl.replace("p.qpic.cn", "p.qlogo.cn")

      return {
        code: 200,
        url: finalUrl,
      }
    } catch (error) {
      // logger.error("腾讯图床上传错误:", error)
      return {
        code: -1,
        msg: error.message,
      }
    }
  }

  // 并行处理图片消息
  async processImagesParallel(imageMessages) {
    const imagePromises = imageMessages.map(async msg => {
      try {
        // logger.mark(`[Markdown转换] 开始处理图片消息`)
        let requestBody = msg.file || msg.url
        requestBody = await Bot.Buffer(requestBody)

        // 并行获取图片尺寸和准备上传数据
        const [sizeResult, base64String] = await Promise.all([
          this.getImageSize(requestBody),
          Promise.resolve(requestBody.toString("base64")),
        ])

        const { width, height } = sizeResult

        // 检查图片高度限制
        if (height > (config.advanced?.image?.maxHeight || 3950)) {
          // logger.warn(
          //   `[Markdown转换] 图片高度${height}超过限制${config.advanced?.image?.maxHeight || 3950}，跳过处理`,
          // )
          throw new Error("图片高度超过限制")
        }

        // 确定最终使用的尺寸
        let finalWidth, finalHeight
        if (config.customImageSize.enabled) {
          // 宽度始终保持原始
          finalWidth = width

          if (height > config.customImageSize.maxHeight) {
            // 超出最大高度，使用设定的一半高度
            finalHeight = config.customImageSize.halfHeight
          } else {
            // 未超出最大高度，使用原始高度
            finalHeight = height
          }
        } else {
          // 未启用自定义尺寸，使用原始缩放逻辑
          finalWidth = Math.floor(width * config.markdownImgScale)
          finalHeight = Math.floor(height * config.markdownImgScale)
        }

        // 上传到腾讯图床
        let imageUrl
        if (config.enableTencentImageBed) {
          // logger.mark(`[Markdown转换] 开始上传图片到腾讯图床`)
          imageUrl = await this.getTencentImageUrl(base64String)
        }

        if (!imageUrl) {
          throw new Error("图片URL为空")
        }

        return {
          url: imageUrl,
          width: finalWidth,
          height: finalHeight,
          originalWidth: width,
          originalHeight: height,
          size: `${finalWidth}px × ${finalHeight}px`,
        }
      } catch (error) {
        // logger.error(`[Markdown转换] 图片处理失败:`, error)
        throw error
      }
    })

    // 使用 Promise.allSettled 来处理部分失败的情况
    const results = await Promise.allSettled(imagePromises)

    // 只返回成功处理的图片
    const successfulImages = results
      .filter(result => result.status === "fulfilled")
      .map(result => result.value)

    // 如果有失败的图片，记录但不影响其他图片
    const failedCount = results.filter(result => result.status === "rejected").length
    if (failedCount > 0) {
      // logger.warn(`[Markdown转换] ${failedCount}张图片处理失败，成功处理${successfulImages.length}张`)
    }

    return successfulImages
  }

  async getImageSize(buffer) {
    try {
      return imageSize(new Uint8Array(buffer))
    } catch (error) {
      // logger.error("获取图片尺寸失败:", error)
      return { width: 0, height: 0 }
    }
  }

  // 检查文本中是否包含链接
  hasLinks(text) {
    // 常见的链接模式
    const linkPatterns = [
      /https?:\/\/[^\s]+/gi, // http/https链接
      /www\.[^\s]+/gi, // www开头的链接
      /[a-zA-Z0-9-]+\.[a-zA-Z]{2,}[^\s]*/gi, // 域名格式
      /qq\.com/gi, // QQ相关链接
      /bilibili\.com/gi, // B站链接
      /github\.com/gi, // GitHub链接
      /baidu\.com/gi, // 百度链接
      /taobao\.com/gi, // 淘宝链接
      /tmall\.com/gi, // 天猫链接
      /jd\.com/gi, // 京东链接
    ]

    return linkPatterns.some(pattern => pattern.test(text))
  }

  // 检查是否是超能力相关消息
  isSuperPowerMessage(text) {
    const superPowerKeywords = [
      "今日超能力",
      "昨日超能力",
      "明日超能力",
      "超能力指数",
      "按下",
      "不按",
      "评论成功",
      "点赞",
      "点踩",
    ]

    return superPowerKeywords.some(keyword => text.includes(keyword))
  }

  // 创建超能力Markdown消息
  createSuperPowerMarkdownMessage(textMessage) {
    try {
      // 保持原始格式，包括换行和段落
      let formattedText = textMessage.trim()

      // 分行处理，识别不同的内容类型
      const lines = formattedText.split("\n")
      const formattedLines = lines.map(line => {
        const trimmedLine = line.trim()

        if (!trimmedLine) return line // 保持空行

        // 格式化不同类型的内容
        if (trimmedLine.includes("超能力")) {
          // 超能力标题
          return `#${trimmedLine}`
        } else if (
          trimmedLine.includes("你是否会按下这个按钮") ||
          trimmedLine.includes("你会按下这个按钮吗")
        ) {
          // 主要问题 - 移除原有的#号，设置为标题
          const cleanQuestion = trimmedLine.replace(/^#+\s*/, "") // 移除开头的#号和空格
          return `## ${cleanQuestion}`
        } else if (trimmedLine.startsWith("#但是:") || trimmedLine.startsWith("但是:")) {
          // "但是"部分标题
          return `### ${trimmedLine}`
        } else if (trimmedLine.startsWith(">已有") && trimmedLine.includes("选择")) {
          // 统计信息
          return `> ${trimmedLine.substring(1)}`
        } else if (trimmedLine.includes("你获得了") || trimmedLine.includes("你拥有")) {
          // 能力描述
          return `*${trimmedLine}*`
        }

        return line // 其他内容保持原样
      })

      formattedText = formattedLines.join("\n")

      const params = [
        {
          key: config.templates.superPower.textKey,
          values: [formattedText],
        },
      ]

      return segment.markdown({
        custom_template_id: config.templates.superPower.id,
        params: params,
      })
    } catch (error) {
      // logger.error("创建超能力Markdown消息出错:", error)
      // 如果创建超能力消息失败，回退到普通文本模式
      const params = [
        {
          key: config.templates.text.textKey,
          values: [textMessage],
        },
      ]

      return segment.markdown({
        custom_template_id: config.templates.text.id,
        params: params,
      })
    }
  }
}
